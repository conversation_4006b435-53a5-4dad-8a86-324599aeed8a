package main

import (
	"context"
	"fmt"
	"log"
	"time"

	"cloud.google.com/go/civil"
	"cloud.google.com/go/spanner"
	"github.com/bereal/backend-go/pkg/config"
	spannerPkg "github.com/bereal/backend-go/pkg/spanner"
)

func main() {
	// Load configuration
	cfg, err := config.Load("./domains/entity/user-grpc-api/config/local.resolved.yaml")
	if err != nil {
		log.Fatalf("Failed to load config: %v", err)
	}

	// Initialize Spanner client
	spannerClient, err := spannerPkg.NewClient(cfg.Spanner)
	if err != nil {
		log.Fatalf("Failed to create Spanner client: %v", err)
	}
	defer spannerClient.Close()

	ctx := context.Background()
	testUserID := "test-user-id-123"

	// Setup test data
	fmt.Println("Setting up test data...")
	if err := setupTestData(ctx, spannerClient, testUserID); err != nil {
		log.Fatalf("Failed to setup test data: %v", err)
	}

	// Note: To fully test the streak recovery functions, you would need to:
	// 1. Initialize the Server struct with all dependencies (momentsClient, postClient, etc.)
	// 2. Create mock data for moments and posts
	// 3. Test the actual gRPC endpoints

	fmt.Println("Test data setup complete!")
	fmt.Println("You can now test the streak recovery functions using:")
	fmt.Println("1. gRPC calls to localhost:8082")
	fmt.Println("2. Unit tests")
	fmt.Println("3. Integration tests")
}

func setupTestData(ctx context.Context, client *spanner.Client, userID string) error {
	// Create test user
	userMutation := spanner.InsertOrUpdate(
		"Users",
		[]string{"UserId", "Username", "Region", "CreatedAt", "UpdatedAt", "UpdatedBy"},
		[]interface{}{userID, "testuser", "europe", time.Now(), time.Now(), "test-setup"},
	)

	// Create test streak
	streakMutation := spanner.InsertOrUpdate(
		"Streaks",
		[]string{"UserId", "Length", "LastPostCalendarDay", "UpdatedAt", "UpdatedBy"},
		[]interface{}{userID, int64(5), civil.Date{Year: 2024, Month: 1, Day: 15}, time.Now(), "test-setup"},
	)

	// Execute mutations
	_, err := client.Apply(ctx, []*spanner.Mutation{userMutation, streakMutation})
	if err != nil {
		return fmt.Errorf("failed to insert test data: %w", err)
	}

	fmt.Printf("Created test user %s with streak length 5\n", userID)
	return nil
}
