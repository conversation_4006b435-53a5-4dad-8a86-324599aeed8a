#!/bin/bash

# Script to populate test data for streak recovery testing
set -e

USER_SERVICE_URL="localhost:8082"
MOMENT_SERVICE_URL="localhost:8086"
POST_SERVICE_URL="localhost:8085"

echo "🗄️ Populating Test Data for Streak Recovery"
echo "============================================="

# Test user IDs
TEST_USER_1="test-user-with-gaps-001"
TEST_USER_2="test-user-perfect-streak-002"
TEST_USER_3="test-user-no-streak-003"

echo "Creating test users..."

# Create User 1 - User with gaps in posting (good candidate for streak recovery)
echo "📝 Creating User 1: $TEST_USER_1 (user with posting gaps)"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_1\",
    \"username\": \"testuser1\",
    \"full_name\": \"Test User One\",
    \"region\": \"europe\",
    \"phone_number\": \"+33123456789\",
    \"created_at\": \"2024-01-01T00:00:00Z\"
}" $USER_SERVICE_URL entity.user.v1.UserService/CreateUser 2>/dev/null || echo "User 1 creation failed (might already exist)"

# Create User 2 - User with perfect streak (no gaps)
echo "📝 Creating User 2: $TEST_USER_2 (user with perfect streak)"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_2\",
    \"username\": \"testuser2\",
    \"full_name\": \"Test User Two\",
    \"region\": \"europe\",
    \"phone_number\": \"+33123456790\",
    \"created_at\": \"2024-01-01T00:00:00Z\"
}" $USER_SERVICE_URL entity.user.v1.UserService/CreateUser 2>/dev/null || echo "User 2 creation failed (might already exist)"

# Create User 3 - User with no streak
echo "📝 Creating User 3: $TEST_USER_3 (user with no streak)"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_3\",
    \"username\": \"testuser3\",
    \"full_name\": \"Test User Three\",
    \"region\": \"europe\",
    \"phone_number\": \"+33123456791\",
    \"created_at\": \"2024-01-01T00:00:00Z\"
}" $USER_SERVICE_URL entity.user.v1.UserService/CreateUser 2>/dev/null || echo "User 3 creation failed (might already exist)"

echo ""
echo "Creating streaks for test users..."

# Create streak for User 1 (with gaps - last posted 5 days ago, but has a streak of 3)
echo "📊 Creating streak for User 1 (3-day streak, last posted 2024-01-10)"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_1\",
    \"length\": 3,
    \"last_post_calendar_day\": {
        \"year\": 2024,
        \"month\": 1,
        \"day\": 10
    },
    \"last_post_moment_id\": \"moment-2024-01-10-europe\"
}" $USER_SERVICE_URL entity.user.v1.UserService/UpdateStreak 2>/dev/null || echo "Streak creation for User 1 failed"

# Create streak for User 2 (perfect streak - posted yesterday)
echo "📊 Creating streak for User 2 (7-day streak, posted yesterday)"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_2\",
    \"length\": 7,
    \"last_post_calendar_day\": {
        \"year\": 2024,
        \"month\": 1,
        \"day\": 14
    },
    \"last_post_moment_id\": \"moment-2024-01-14-europe\"
}" $USER_SERVICE_URL entity.user.v1.UserService/UpdateStreak 2>/dev/null || echo "Streak creation for User 2 failed"

# User 3 intentionally has no streak

echo ""
echo "✅ Test data population complete!"
echo ""
echo "Test Users Created:"
echo "  1. $TEST_USER_1 - Has 3-day streak, last posted 2024-01-10 (has gaps)"
echo "  2. $TEST_USER_2 - Has 7-day streak, last posted 2024-01-14 (perfect)"
echo "  3. $TEST_USER_3 - No streak data (new user)"
echo ""
echo "Ready for testing! 🚀"
