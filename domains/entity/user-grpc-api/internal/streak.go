package internal

import (
	"context"
	"fmt"
	"sort"
	"time"

	"cloud.google.com/go/civil"
	gS "cloud.google.com/go/spanner"
	entity "github.com/BeReal-App/backend-go/domains/entity"
	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	"github.com/BeReal-App/backend-go/domains/entity/user-grpc-api/internal/metrics"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	pbPost "github.com/BeReal-App/backend-go/proto/private/post/v2"
	"github.com/BeReal-App/backend-go/shared/model"
	"github.com/BeReal-App/backend-go/shared/scope"
	"github.com/BeReal-App/backend-go/shared/util"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Server) GetStreak(ctx context.Context, req *pbUser.GetStreakRequest) (*pbUser.GetStreakResponse, error) {
	row, err := s.spannerClient.Single().ReadRowWithOptions(
		ctx,
		entity.UserStreakTableName,
		gS.Key{req.UserId},
		entity.UserStreakDefaultMapper.Columns(),
		&gS.ReadOptions{RequestTag: "GetStreak"},
	)
	if err != nil {
		if gS.ErrCode(err) == codes.NotFound {
			return nil, status.Errorf(codes.NotFound, "UserStreak not found [userId=%s]", req.UserId)
		}
		return nil, fmt.Errorf("Single().ReadRowWithOptions() [userId=%s]: %w", req.UserId, err)
	}

	var streak entity.UserStreak
	if err = row.Columns(entity.UserStreakDefaultMapper.Addrs(&streak)...); err != nil {
		return nil, fmt.Errorf("row.Columns() [userId=%s]: %w", req.UserId, err)
	}

	// Get current and previous moments with error handling
	moments := s.momentsClient.GetCurrentAndPreviousByRegion()
	if moments == nil {
		// Log warning but don't fail the request - fallback to not resetting the streak
		l := scope.GetLoggerForCallsite(ctx, "GetStreak")
		l.Warn().Msg("failed to get current and previous moments, using empty slice")
		moments = []models.Moment{}
	}

	return &pbUser.GetStreakResponse{
		Streak: correctStreak(ctx, streak, moments),
	}, nil
}

func (s *Server) GetStreaks(ctx context.Context, req *pbUser.GetStreaksRequest) (*pbUser.GetStreaksResponse, error) {
	userIDs := req.GetUserIds()
	streaks := make(map[string]*pbUser.Streak)

	// Get current and previous moments with error handling
	moments := s.momentsClient.GetCurrentAndPreviousByRegion()
	if moments == nil {
		// Log warning but don't fail the request - fallback to not resetting the streak
		l := scope.GetLoggerForCallsite(ctx, "GetStreaks")
		l.Warn().Msg("failed to get current and previous moments, using empty slice")
		moments = []models.Moment{}
	}

	// Convert userIDs to Spanner KeySet
	keys := make([]gS.Key, len(userIDs))
	for i, id := range userIDs {
		keys[i] = gS.Key{id}
	}
	keySet := gS.KeySetFromKeys(keys...)

	// Use ReadWithOptions for efficient key-based reads
	iter := s.spannerClient.Single().ReadWithOptions(
		ctx,
		entity.UserStreakTableName,
		keySet,
		entity.UserStreakDefaultMapper.Columns(),
		&gS.ReadOptions{RequestTag: "GetStreaks"},
	)

	err := iter.Do(func(row *gS.Row) error {
		var streak entity.UserStreak
		if err := row.Columns(entity.UserStreakDefaultMapper.Addrs(&streak)...); err != nil {
			return fmt.Errorf("row.Columns() [userId=%s]: %w", streak.UserID, err)
		}
		streaks[streak.UserID] = correctStreak(ctx, streak, moments)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &pbUser.GetStreaksResponse{
		Streaks: streaks,
	}, nil
}

func (s *Server) UpdateStreak(ctx context.Context, req *pbUser.UpdateStreakRequest) (*pbUser.UpdateStreakResponse, error) {
	e := entity.UserStreak{
		UserID:              req.UserId,
		Length:              int64(req.Length),
		LastPostCalendarDay: req.LastPostCalendarDay.SpannerNullDate(),
		LastPostMomentID:    gS.NullString{StringVal: req.LastPostMomentId, Valid: req.LastPostMomentId != ""},
		UpdatedAt:           gS.CommitTimestamp,
		UpdatedBy:           util.UpdationSourceFromCtx(ctx),
	}

	resp, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite([]*gS.Mutation{gS.InsertOrUpdate(
				entity.UserStreakTableName,
				entity.UserStreakDefaultMapper.Columns(),
				entity.UserStreakDefaultMapper.Values(&e),
			)})
		},
		gS.TransactionOptions{
			CommitOptions:  gS.CommitOptions{MaxCommitDelay: s.streakCommitDelay, ReturnCommitStats: false},
			TransactionTag: "UpdateStreak",
		},
	)
	if err != nil {
		return nil, fmt.Errorf("on s.spannerClient.ReadWriteTransactionWithOptions() [userId=%s]: %w", req.UserId, err)
	}

	return &pbUser.UpdateStreakResponse{
		UpdatedAt: timestamppb.New(resp.CommitTs),
	}, nil
}

func correctStreak(ctx context.Context, streak entity.UserStreak, moments []models.Moment) *pbUser.Streak {
	// Create a copy to avoid modifying the original parameter
	streakCopy := streak

	if streakCopy.Length > 0 && streakCopy.LastPostMomentID.Valid {
		isOutdated := isStreakOutdated(ctx, streakCopy.LastPostMomentID.StringVal, moments)

		// Track metric for outdated/not outdated streaks
		if isOutdated {
			metrics.StreakStatusCounter.WithLabelValues("true").Inc()
			streakCopy.Length = 0
			streakCopy.LastPostCalendarDay = gS.NullDate{}
		} else {
			metrics.StreakStatusCounter.WithLabelValues("false").Inc()
		}
	}

	return streakCopy.Proto()
}

// isStreakOutdated checks if the streak is outdated by verifying if the LastPostMomentID does NOT exist
// in the current and previous moments. If the last post was made in an older moment (not in current or previous),
// then the streak should be reset to 0.
func isStreakOutdated(ctx context.Context, lastPostMomentID string, moments []models.Moment) bool {
	l := scope.GetLoggerForCallsite(ctx, "isStreakOutdated")

	if moments == nil {
		l.Warn().Msg("moments slice is nil, considering streak not outdated")
		return false
	}

	// Check if the LastPostMomentID exists in current and previous moments
	for _, moment := range moments {
		if moment.ID == lastPostMomentID {
			return false
		}
	}

	return true
}

// CalculateStreakRecovery calculates what the user's streak would be if missing days were filled
func (s *Server) CalculateStreakRecovery(ctx context.Context, req *pbUser.CalculateStreakRecoveryRequest) (*pbUser.CalculateStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "CalculateStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int64("number_of_days", int64(req.NumberOfDays)).
		Msg("Starting streak recovery calculation")

	// Determine the number of days to look back, defaulting to 30 if not set or invalid
	numberOfDays := int(req.NumberOfDays)
	if numberOfDays <= 0 {
		l.Debug().
			Str("user_id", req.UserId).
			Int64("requested_days", int64(req.NumberOfDays)).
			Int("default_days", 30).
			Msg("Invalid number of days provided, using default")
		numberOfDays = 30
	}

	// Get user's basic info to determine region
	l.Debug().Str("user_id", req.UserId).Msg("Fetching user basic info")
	userBasicInfo, err := s.getUserBasicInfo(ctx, req.UserId)
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get user basic info")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: fmt.Sprintf("Failed to get user info: %v", err),
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userBasicInfo.Region).
		Msg("Retrieved user basic info")

	// Get current streak
	l.Debug().Str("user_id", req.UserId).Msg("Fetching current streak")
	currentStreak, err := s.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get current streak")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: fmt.Sprintf("Failed to get current streak: %v", err),
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Uint64("current_streak", currentStreak.Streak.Length).
		Msg("Retrieved current streak")

	// Parse user's region
	userRegion := model.NewRegion(userBasicInfo.Region)
	if userRegion == model.RegionUnknown {
		l.Error().
			Str("user_id", req.UserId).
			Str("region", userBasicInfo.Region).
			Msg("Invalid user region")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "Invalid user region",
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Msg("Parsed user region")

	// Get all moments for the user's region
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Msg("Fetching moments for user region")
	allMomentsByRegion := s.momentsClient.GetAllByRegion()
	userRegionMoments, exists := allMomentsByRegion[userRegion]
	if !exists || userRegionMoments == nil || userRegionMoments.Len() == 0 {
		momentsCount := 0
		if userRegionMoments != nil {
			momentsCount = userRegionMoments.Len()
		}
		l.Error().
			Str("user_id", req.UserId).
			Str("region", userRegion.String()).
			Bool("exists", exists).
			Int("moments_count", momentsCount).
			Msg("No moments found for user region")
		return &pbUser.CalculateStreakRecoveryResponse{
			Calculation: &pbUser.StreakRecoveryCalculation{
				IsEligible:   false,
				ErrorMessage: "No moments found for user region",
			},
		}, nil
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userRegion.String()).
		Int("moments_count", userRegionMoments.Len()).
		Msg("Retrieved moments for user region")

	// Sort moments by FiredAt desc (most recent first)
	userRegionMomentsList := userRegionMoments.AsMutableSlice()
	l.Debug().
		Str("user_id", req.UserId).
		Int("total_moments", len(userRegionMomentsList)).
		Msg("Processing moments for sorting")

	sortedMoments := make([]models.Moment, 0, userRegionMoments.Len())
	var unfiredCount int
	for _, moment := range userRegionMomentsList {
		// Only include fired moments
		if moment.FiredAt != nil {
			sortedMoments = append(sortedMoments, moment)
		} else {
			unfiredCount++
		}
	}
	l.Debug().
		Str("user_id", req.UserId).
		Int("fired_moments", len(sortedMoments)).
		Int("unfired_moments", unfiredCount).
		Msg("Filtered out unfired moments")

	sort.Slice(sortedMoments, func(i, j int) bool {
		return sortedMoments[i].FiredAt.After(*sortedMoments[j].FiredAt)
	})
	l.Debug().
		Str("user_id", req.UserId).
		Int("sorted_moments", len(sortedMoments)).
		Msg("Sorted moments by FiredAt (descending)")

	// Get all user's posts (no time limit for estimated streak calculation)
	l.Debug().Str("user_id", req.UserId).Msg("Fetching all user posts:")
	userPosts, err := s.getAllUserPosts(ctx, req.UserId)
	if err != nil {
		l.Warn().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get user posts, continuing with empty slice")
		userPosts = []UserPost{}
	}
	l.Debug().
		Str("user_id", req.UserId).
		Int("posts_count", len(userPosts)).
		Msg("Retrieved user posts")

	// Create a map of moment IDs to check if user posted
	postsByMomentID := make(map[string]bool)
	for _, post := range userPosts {
		if post.MomentID.Valid {
			postsByMomentID[post.MomentID.StringVal] = true
		}
	}
	l.Debug().
		Str("user_id", req.UserId).
		Int("unique_moment_posts", len(postsByMomentID)).
		Msg("Created map of posts by moment ID")

	// Find gaps in the last N days (recovery is only allowed for recent gaps)
	now := time.Now()
	lookbackAgo := now.AddDate(0, 0, -numberOfDays)
	l.Debug().
		Str("user_id", req.UserId).
		Time("now", now).
		Time("lookback_threshold", lookbackAgo).
		Int("lookback_days", numberOfDays).
		Msg("Calculating lookback period for gap detection")

	var gapsToFill []*pbUser.StreakGap
	// Track which dates have already been added
	dateAdded := make(map[string]bool)

	for _, moment := range sortedMoments {
		// Only consider moments from the last N days for gap filling
		if moment.FiredAt.Before(lookbackAgo) {
			continue
		}

		// Generate a date key in YYYY-MM-DD format
		dateKey := moment.FiredAt.Format("2006-01-02")

		// Skip if we've already added a gap for this date
		if dateAdded[dateKey] {
			continue
		}

		// Check if user posted for this moment
		if !postsByMomentID[moment.ID] {
			gapsToFill = append(gapsToFill, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(moment.FiredAt.Year()),
					Month: int32(moment.FiredAt.Month()),
					Day:   int32(moment.FiredAt.Day()),
				},
				MomentId: moment.ID,
			})
			dateAdded[dateKey] = true

			l.Debug().
				Str("user_id", req.UserId).
				Str("moment_id", moment.ID).
				Str("date", dateKey).
				Msg("Found gap to fill")
		}
	}

	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_found", len(gapsToFill)).
		Msg("Identified gaps to fill for streak recovery")

	// Calculate estimated streak after filling gaps (uses all posts to find true break point)
	l.Debug().
		Str("user_id", req.UserId).
		Int("gaps_to_fill", len(gapsToFill)).
		Msg("Calculating estimated streak")
	estimatedStreak := s.calculateEstimatedStreak(ctx, req.UserId, sortedMoments, postsByMomentID, gapsToFill)

	isEligible := len(gapsToFill) > 0 && len(gapsToFill) <= 10

	l.Info().
		Str("user_id", req.UserId).
		Uint64("current_streak", currentStreak.Streak.Length).
		Uint64("estimated_streak", estimatedStreak).
		Int("gaps_count", len(gapsToFill)).
		Bool("is_eligible", isEligible).
		Msg("Completed streak recovery calculation")

	return &pbUser.CalculateStreakRecoveryResponse{
		Calculation: &pbUser.StreakRecoveryCalculation{
			CurrentStreak:   currentStreak.Streak.Length,
			EstimatedStreak: estimatedStreak,
			GapsToFill:      gapsToFill,
			IsEligible:      isEligible,
		},
	}, nil
}

// ApplyStreakRecovery applies streak recovery by inserting compensation records and recomputing the streak
func (s *Server) ApplyStreakRecovery(ctx context.Context, req *pbUser.ApplyStreakRecoveryRequest) (*pbUser.ApplyStreakRecoveryResponse, error) {
	l := scope.GetLoggerForCallsite(ctx, "ApplyStreakRecovery")

	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_count", len(req.GapsToFill)).
		Msg("Starting streak recovery application")

	// Validate input
	if req.UserId == "" {
		l.Error().Msg("Invalid user ID: empty")
		return nil, status.Errorf(codes.InvalidArgument, "invalid user ID: empty")
	}

	if len(req.GapsToFill) == 0 {
		l.Error().
			Str("user_id", req.UserId).
			Msg("No gaps provided for recovery")
		return nil, status.Errorf(codes.InvalidArgument, "no gaps provided for recovery")
	}

	if len(req.GapsToFill) > 10 {
		l.Error().
			Str("user_id", req.UserId).
			Int("gaps_count", len(req.GapsToFill)).
			Msg("Too many gaps for recovery (max 10)")
		return nil, status.Errorf(codes.InvalidArgument, "too many gaps for recovery: %d (max 10)", len(req.GapsToFill))
	}

	// Log gap details
	for i, gap := range req.GapsToFill {
		l.Debug().
			Str("user_id", req.UserId).
			Int("gap_index", i).
			Str("moment_id", gap.MomentId).
			Int32("year", gap.Date.Year).
			Int32("month", gap.Date.Month).
			Int32("day", gap.Date.Day).
			Msg("Gap details for filling")
	}

	// Get user's current streak before applying recovery
	currentStreak, err := s.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get current streak before applying recovery")
	} else {
		l.Info().
			Str("user_id", req.UserId).
			Uint64("current_streak", currentStreak.Streak.Length).
			Msg("Current streak before applying recovery")
	}

	// Get user's basic info to verify they exist
	l.Debug().
		Str("user_id", req.UserId).
		Msg("Fetching user basic info")
	userBasicInfo, err := s.getUserBasicInfo(ctx, req.UserId)
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get user info")
		return nil, fmt.Errorf("failed to get user info for userID %s: %w", req.UserId, err)
	}
	l.Debug().
		Str("user_id", req.UserId).
		Str("region", userBasicInfo.Region).
		Msg("Retrieved user basic info")

	// Insert StreakCompensation records
	l.Debug().
		Str("user_id", req.UserId).
		Int("gaps_count", len(req.GapsToFill)).
		Msg("Inserting streak compensation records")
	err = s.insertStreakCompensationRecords(ctx, req.UserId, req.GapsToFill)
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to insert streak compensation records")
		return nil, fmt.Errorf("failed to insert streak compensation records: %w", err)
	}
	l.Info().
		Str("user_id", req.UserId).
		Int("gaps_filled", len(req.GapsToFill)).
		Msg("Successfully inserted streak compensation records")

	// Get the updated streak after compensation
	l.Debug().
		Str("user_id", req.UserId).
		Msg("Fetching updated streak after compensation")
	newStreakResponse, err := s.GetStreak(ctx, &pbUser.GetStreakRequest{UserId: req.UserId})
	if err != nil {
		l.Error().
			Str("user_id", req.UserId).
			Err(err).
			Msg("Failed to get updated streak after compensation")
		return nil, fmt.Errorf("failed to get updated streak after compensation: %w", err)
	}

	// Calculate streak difference
	var streakDifference uint64
	if currentStreak != nil {
		streakDifference = newStreakResponse.Streak.Length - currentStreak.Streak.Length
	}

	l.Info().
		Str("user_id", req.UserId).
		Uint64("old_streak_length", func() uint64 {
			if currentStreak != nil {
				return currentStreak.Streak.Length
			}
			return 0
		}()).
		Uint64("new_streak_length", newStreakResponse.Streak.Length).
		Uint64("streak_increase", streakDifference).
		Str("region", userBasicInfo.Region).
		Msg("Completed streak recovery successfully")

	return &pbUser.ApplyStreakRecoveryResponse{
		NewStreak: newStreakResponse.Streak,
	}, nil
}

// insertStreakCompensationRecords inserts multiple StreakCompensation records in a single transaction
func (s *Server) insertStreakCompensationRecords(ctx context.Context, userID string, gaps []*pbUser.StreakGap) error {
	l := scope.GetLoggerForCallsite(ctx, "insertStreakCompensationRecords")

	l.Info().
		Str("user_id", userID).
		Int("gaps_count", len(gaps)).
		Msg("Starting to insert streak compensation records")

	// Prepare mutations for batch insert
	var mutations []*gS.Mutation
	for _, gap := range gaps {
		// Convert pbCommon.Date to civil.Date for the Date column
		spannerDate := civil.Date{
			Year:  int(gap.Date.Year),
			Month: time.Month(gap.Date.Month),
			Day:   int(gap.Date.Day),
		}

		mutation := gS.InsertOrUpdate(
			"StreakCompensation",
			[]string{"UserId", "Date", "MomentID"},
			[]interface{}{userID, spannerDate, gap.MomentId},
		)
		mutations = append(mutations, mutation)

		l.Debug().
			Str("user_id", userID).
			Str("date", spannerDate.String()).
			Str("moment_id", gap.MomentId).
			Msg("Prepared StreakCompensation mutation")
	}

	l.Debug().
		Str("user_id", userID).
		Int("mutations_count", len(mutations)).
		Msg("Executing batch insert transaction")

	// Execute batch insert in a transaction
	_, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite(mutations)
		},
		gS.TransactionOptions{
			TransactionTag: "ApplyStreakRecovery",
		},
	)
	if err != nil {
		l.Error().
			Str("user_id", userID).
			Err(err).
			Msg("Failed to insert StreakCompensation records")
		return fmt.Errorf("failed to insert StreakCompensation records: %w", err)
	}

	l.Info().
		Str("user_id", userID).
		Int("records_inserted", len(gaps)).
		Msg("Successfully inserted compensation records")

	return nil
}

// UserPost represents a simplified post structure for streak calculations
type UserPost struct {
	PostID    string
	UserID    string
	MomentID  gS.NullString
	CreatedAt time.Time
}

// getAllUserPosts retrieves all user posts (no time limit) via post service
func (s *Server) getAllUserPosts(ctx context.Context, userID string) ([]UserPost, error) {
	// Call the post service to get user posts
	resp, err := s.postClient.GetPostsOfUser(ctx, &pbPost.GetPostsOfUserRequest{
		UserId: userID,
		Order: &pbPost.OrderBy{
			Field:     pbPost.OrderBy_FIELD_CREATED_AT,
			Direction: pbPost.OrderBy_DIRECTION_DESC,
		},
		IncludeDeleted: func() *bool { b := false; return &b }(), // Exclude deleted posts for streak calculation
		SearchAll:      func() *bool { b := true; return &b }(),  // Search full table for all posts
		// No limit - we need all posts for accurate streak calculation
	})
	if err != nil {
		return nil, fmt.Errorf("failed to get user posts from post service: %w", err)
	}

	// Convert proto posts to UserPost structs
	var posts []UserPost
	for _, protoPost := range resp.Posts {
		post := UserPost{
			PostID:    protoPost.Id,
			UserID:    protoPost.UserId,
			CreatedAt: protoPost.CreatedAt.AsTime(),
		}

		// Set MomentID if available
		if protoPost.MomentId != "" {
			post.MomentID = gS.NullString{StringVal: protoPost.MomentId, Valid: true}
		} else {
			post.MomentID = gS.NullString{Valid: false}
		}

		posts = append(posts, post)
	}

	return posts, nil
}

// calculateEstimatedStreak calculates what the streak would be if gaps were filled
// This function uses all user posts (not time-limited) to determine where the streak would break
func (s *Server) calculateEstimatedStreak(_ context.Context, _ string, sortedMoments []models.Moment, postsByMomentID map[string]bool, gapsToFill []*pbUser.StreakGap) uint64 {
	// Create a map of gaps to fill for easier lookup
	gapMomentIDs := make(map[string]bool)
	for _, gap := range gapsToFill {
		gapMomentIDs[gap.MomentId] = true
	}

	// Calculate streak from most recent moment backwards
	// Since we have all user posts, we can accurately determine where the streak would break
	consecutiveDays := 0

	for _, moment := range sortedMoments {
		// Check if user posted or if this is a gap we plan to fill
		hasPost := postsByMomentID[moment.ID] || gapMomentIDs[moment.ID]

		if hasPost {
			consecutiveDays++
		} else {
			// Break in the streak - stop counting
			break
		}
	}

	return uint64(consecutiveDays)
}

// sortGapsByDate sorts streak gaps by date (oldest first)
func sortGapsByDate(gaps []*pbUser.StreakGap) {
	sort.Slice(gaps, func(i, j int) bool {
		dateI := gaps[i].Date
		dateJ := gaps[j].Date

		if dateI.Year != dateJ.Year {
			return dateI.Year < dateJ.Year
		}
		if dateI.Month != dateJ.Month {
			return dateI.Month < dateJ.Month
		}
		return dateI.Day < dateJ.Day
	})
}

// calculateGapsFromPosts calculates streak gaps from user posts within a lookback period
func (s *Server) calculateGapsFromPosts(_ context.Context, _ string, userPosts map[string]bool, moments []models.Moment, lookbackDays int64, _ civil.Date, _ string) ([]*pbUser.StreakGap, error) {
	var gaps []*pbUser.StreakGap
	now := time.Now()
	lookbackTime := now.AddDate(0, 0, -int(lookbackDays))

	for _, moment := range moments {
		// Skip if moment is outside lookback period
		if moment.FiredAt != nil && moment.FiredAt.Before(lookbackTime) {
			continue
		}

		// Convert moment date to string format for comparison
		var momentDateStr string
		if moment.FiredAt != nil {
			momentDateStr = moment.FiredAt.Format("2006-01-02")
		} else {
			// Skip unfired moments
			continue
		}

		// Check if user posted on this day
		if !userPosts[momentDateStr] {
			gaps = append(gaps, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(moment.FiredAt.Year()),
					Month: int32(moment.FiredAt.Month()),
					Day:   int32(moment.FiredAt.Day()),
				},
				MomentId: moment.ID,
			})
		}
	}

	// Sort gaps by date (oldest first)
	sortGapsByDate(gaps)

	return gaps, nil
}
