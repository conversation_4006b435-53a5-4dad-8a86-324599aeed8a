#!/bin/bash

# Test script for CalculateStreakRecovery and ApplyStreakRecovery functions
# This script uses grpcurl to test the gRPC endpoints

set -e

USER_SERVICE_URL="localhost:8082"
TEST_USER_ID="test-user-$(date +%s)"

echo "🧪 Testing Streak Recovery Functions"
echo "====================================="
echo "User Service URL: $USER_SERVICE_URL"
echo "Test User ID: $TEST_USER_ID"
echo ""

# Check if grpcurl is installed
if ! command -v grpcurl &> /dev/null; then
    echo "❌ grpcurl is not installed. Please install it first:"
    echo "   brew install grpcurl"
    echo "   or"
    echo "   go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest"
    exit 1
fi

# Check if the user service is running
echo "🔍 Checking if user service is running..."
if ! nc -z localhost 8082; then
    echo "❌ User service is not running on port 8082"
    echo "   Please start it with: make start-entity-user-grpc-api"
    exit 1
fi
echo "✅ User service is running"
echo ""

# Test 1: Get current streak
echo "📊 Getting current streak for a test user..."
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\"
}" $USER_SERVICE_URL entity.user.v1.UserService/GetStreak

echo ""

# Test 2: Calculate streak recovery
echo "🧮 Testing CalculateStreakRecovery..."
echo "Request: Calculate streak recovery for user $TEST_USER_ID"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"number_of_days\": 30
}" $USER_SERVICE_URL entity.user.v1.UserService/CalculateStreakRecovery

echo ""
echo "✅ Testing complete!"
echo ""
echo "💡 Tips for further testing:"
echo "   1. Create test users with actual streak data"
echo "   2. Test with different user regions"
echo "   3. Test with users who have actual posting gaps"
echo "   4. Test edge cases like users with no streaks"
echo "   5. Test with invalid user IDs"
