#!/bin/bash

# Test script for CalculateStreakRecovery and ApplyStreakRecovery functions
# This script uses grpcurl to test the gRPC endpoints

set -e

USER_SERVICE_URL="localhost:8082"
TEST_USER_ID="test-user-$(date +%s)"

echo "🧪 Testing Streak Recovery Functions"
echo "====================================="
echo "User Service URL: $USER_SERVICE_URL"
echo "Test User ID: $TEST_USER_ID"
echo ""

# Check if grpcurl is installed
if ! command -v grpcurl &> /dev/null; then
    echo "❌ grpcurl is not installed. Please install it first:"
    echo "   brew install grpcurl"
    echo "   or"
    echo "   go install github.com/fullstorydev/grpcurl/cmd/grpcurl@latest"
    exit 1
fi

# Check if the user service is running
echo "🔍 Checking if user service is running..."
if ! nc -z localhost 8082; then
    echo "❌ User service is not running on port 8082"
    echo "   Please start it with: make start-entity-user-grpc-api"
    exit 1
fi
echo "✅ User service is running"
echo ""

# Test 1: Create a test user and streak (this might fail if user already exists, which is fine)
echo "📝 Setting up test data..."
echo "Creating test user..."
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"username\": \"testuser\",
    \"region\": \"europe\"
}" $USER_SERVICE_URL entity.user.v1.UserService/CreateUser 2>/dev/null || echo "User creation failed (might already exist)"

echo "Creating test streak..."
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"length\": 5,
    \"last_post_calendar_day\": {
        \"year\": 2024,
        \"month\": 1,
        \"day\": 15
    }
}" $USER_SERVICE_URL entity.user.v1.UserService/UpdateStreak 2>/dev/null || echo "Streak creation failed"

echo ""

# Test 2: Get current streak
echo "📊 Getting current streak..."
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\"
}" $USER_SERVICE_URL entity.user.v1.UserService/GetStreak

echo ""

# Test 3: Calculate streak recovery
echo "🧮 Testing CalculateStreakRecovery..."
echo "Request: Calculate streak recovery for user $TEST_USER_ID"
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"number_of_days\": 30
}" $USER_SERVICE_URL entity.user.v1.UserService/CalculateStreakRecovery

echo ""

# Test 4: Apply streak recovery (if there are gaps to fill)
echo "🔧 Testing ApplyStreakRecovery..."
echo "Note: This will only work if there are actual gaps to fill"
echo "Request: Apply streak recovery for user $TEST_USER_ID"

# Example with sample gaps (you would normally get these from CalculateStreakRecovery)
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\",
    \"gaps_to_fill\": [
        {
            \"date\": {
                \"year\": 2024,
                \"month\": 1,
                \"day\": 16
            },
            \"moment_id\": \"sample-moment-id-1\"
        }
    ]
}" $USER_SERVICE_URL entity.user.v1.UserService/ApplyStreakRecovery

echo ""

# Test 5: Get streak after recovery
echo "📈 Getting streak after recovery..."
grpcurl -plaintext -d "{
    \"user_id\": \"$TEST_USER_ID\"
}" $USER_SERVICE_URL entity.user.v1.UserService/GetStreak

echo ""
echo "✅ Testing complete!"
echo ""
echo "💡 Tips for further testing:"
echo "   1. Modify the test data to create different scenarios"
echo "   2. Test with different user regions"
echo "   3. Test with users who have actual posting gaps"
echo "   4. Test edge cases like users with no streaks"
echo "   5. Test with invalid user IDs"
