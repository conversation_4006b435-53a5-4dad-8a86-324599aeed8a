package main

import (
	"context"
	"fmt"
	"log"
	"os"
	"time"

	"cloud.google.com/go/spanner"
)

func main() {
	ctx := context.Background()

	// Connect to Spanner emulator
	// Set the emulator host environment variable
	os.Setenv("SPANNER_EMULATOR_HOST", "localhost:9010")

	// Create client with emulator endpoint
	client, err := spanner.NewClient(ctx, "projects/test-project/instances/test-instance/databases/test-database")
	if err != nil {
		log.Fatalf("Failed to create Spanner client: %v", err)
	}
	defer client.Close()

	fmt.Println("🗄️ Populating Moment Test Data")
	fmt.Println("===============================")

	// Create moments for the last 30 days for europe-west region
	now := time.Now()
	var mutations []*spanner.Mutation

	for i := 0; i < 30; i++ {
		momentDate := now.AddDate(0, 0, -i)

		// Create moment ID
		momentID := fmt.Sprintf("moment-%s-europe-west", momentDate.Format("2006-01-02"))

		// Set fired time to a random time during the day (between 9 AM and 6 PM)
		firedAt := time.Date(
			momentDate.Year(), momentDate.Month(), momentDate.Day(),
			9+i%9, // Hour between 9-17
			i%60,  // Random minute
			0, 0,  // Second, nanosecond
			time.UTC,
		)

		// End time is 2 hours after fired time
		endAt := firedAt.Add(2 * time.Hour)

		mutation := spanner.InsertOrUpdate(
			"Moments",
			[]string{"ID", "Region", "ScheduledAt", "FiredAt", "EndAt", "Timezone", "LocalTime", "LocalDate"},
			[]interface{}{
				momentID,
				"europe-west",
				firedAt.Add(-1 * time.Hour), // Scheduled 1 hour before firing
				firedAt,
				endAt,
				"Europe/Paris",
				firedAt.Format("15:04:05"),
				firedAt.Format("2006-01-02"),
			},
		)
		mutations = append(mutations, mutation)

		fmt.Printf("📅 Creating moment: %s (fired at %s)\n", momentID, firedAt.Format("2006-01-02 15:04"))
	}

	// Execute batch insert
	fmt.Printf("\n💾 Inserting %d moments into Spanner...\n", len(mutations))
	_, err = client.Apply(ctx, mutations)
	if err != nil {
		log.Fatalf("Failed to insert moments: %v", err)
	}

	fmt.Println("✅ Successfully populated moment data!")
	fmt.Printf("   Created %d moments for europe-west region\n", len(mutations))
	fmt.Println("   Date range: last 30 days")
	fmt.Println("   Ready for streak recovery testing! 🚀")
}
